// Modificación para enviar al endpoint correcto
// Reemplazar la URL del endpoint en handleSaveProspect
async function handleSaveProspectPatch() {
    // Cuando se llama a esta función, patch el envío para usar el nuevo endpoint
    
    // Sobrescribir el método original
    const originalHandleSaveProspect = window.handleSaveProspect;
    
    if (originalHandleSaveProspect) {
        window.handleSaveProspect = async function() {
            console.log('[PATCH] Interceptando handleSaveProspect para usar el nuevo endpoint');
            
            // Recuperar todo el código del método original
            // pero cambiar la URL del endpoint
            try {
                // Todo el código original se ejecuta
                // Pero modificamos la parte del fetch para usar nuestro nuevo endpoint
                
                // El resto del código es manejado por la función original
                // Esta es solo una modificación parcial
                
                // Modificar la implementación global para futuros envíos
                const originalFetch = window.fetch;
                
                window.fetch = function(url, options) {
                    // Verificar si se está llamando al endpoint que queremos reemplazar
                    if (url === 'guardar_inteletgroup_prospecto.php') {
                        console.log('[PATCH] Redirigiendo fetch a guardar_prospecto_inteletgroup.php');
                        return originalFetch('guardar_prospecto_inteletgroup.php', options);
                    }
                    
                    // Para cualquier otra URL, usar el fetch normal
                    return originalFetch(url, options);
                };
                
                // Llamar a la función original que ahora usará nuestro fetch modificado
                return await originalHandleSaveProspect.apply(this, arguments);
                
            } catch (error) {
                console.error('[PATCH] Error en la función patched:', error);
                throw error;
            } finally {
                // Restaurar el fetch original para evitar efectos secundarios
                window.fetch = originalFetch;
            }
        };
        
        console.log('[PATCH] Función handleSaveProspect patched correctamente');
    } else {
        console.error('[PATCH] No se pudo encontrar la función handleSaveProspect');
    }
}

// Ejecutar el patch cuando se cargue el documento
document.addEventListener('DOMContentLoaded', function() {
    console.log('[PATCH] Instalando patch para inteletgroup-prospect.js');
    setTimeout(handleSaveProspectPatch, 1000); // Esperar 1 segundo para asegurar que el script original se haya cargado
});