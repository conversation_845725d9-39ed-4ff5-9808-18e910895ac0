<?php
// Configuración de errores para desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Establecer cabeceras para respuesta JSON
header('Content-Type: application/json');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Incluir archivo de conexión
require_once 'con_db.php';

// Verificar si la conexión fue exitosa
if (!isset($conexion) || $conexion->connect_error) {
    echo json_encode([
        'success' => false,
        'message' => 'Error de conexión a la base de datos'
    ]);
    exit;
}

// Obtener los datos del formulario
$nombre_ejecutivo = $_POST['nombre_ejecutivo'] ?? '';
$rut_cliente = $_POST['rut_cliente'] ?? '';
$razon_social = $_POST['razon_social'] ?? '';
$rubro = $_POST['rubro'] ?? '';
$direccion_comercial = $_POST['direccion_comercial'] ?? '';
$telefono_celular = $_POST['telefono_celular'] ?? '';
$email = $_POST['email'] ?? '';
$tipo_persona = $_POST['tipo_persona'] ?? '';
$numero_pos = $_POST['numero_pos'] ?? '';
$tipo_cuenta = $_POST['tipo_cuenta'] ?? '';
$numero_cuenta_bancaria = $_POST['numero_cuenta_bancaria'] ?? '';
$dias_atencion = $_POST['dias_atencion'] ?? '';
$horario_atencion = $_POST['horario_atencion'] ?? '';
$contrata_boleta = $_POST['contrata_boleta'] ?? '';
$competencia_actual = $_POST['competencia_actual'] ?? '';

// Validar campos requeridos
if (empty($rut_cliente) || empty($razon_social) || empty($rubro) || 
    empty($direccion_comercial) || empty($telefono_celular) || empty($email) || 
    empty($tipo_persona) || empty($tipo_cuenta) || empty($numero_cuenta_bancaria) || 
    empty($dias_atencion) || empty($horario_atencion) || empty($contrata_boleta) || 
    empty($competencia_actual)) {
    
    echo json_encode([
        'success' => false,
        'message' => 'Todos los campos requeridos deben ser completados'
    ]);
    exit;
}

// Fecha de registro
$fecha_registro = date('Y-m-d H:i:s');

try {
    // Crear tabla si no existe
    $tabla_sql = "CREATE TABLE IF NOT EXISTS inteletgroup_prospectos (
        id INT(11) NOT NULL AUTO_INCREMENT,
        nombre_ejecutivo VARCHAR(255) DEFAULT NULL,
        rut_cliente VARCHAR(20) NOT NULL,
        razon_social VARCHAR(255) NOT NULL,
        rubro VARCHAR(255) DEFAULT NULL,
        direccion_comercial TEXT DEFAULT NULL,
        telefono_celular VARCHAR(20) DEFAULT NULL,
        email VARCHAR(255) DEFAULT NULL,
        tipo_persona VARCHAR(50) DEFAULT NULL,
        numero_pos VARCHAR(50) DEFAULT NULL,
        tipo_cuenta VARCHAR(50) DEFAULT NULL,
        numero_cuenta_bancaria VARCHAR(50) DEFAULT NULL,
        dias_atencion VARCHAR(100) DEFAULT NULL,
        horario_atencion VARCHAR(100) DEFAULT NULL,
        contrata_boleta VARCHAR(10) DEFAULT NULL,
        competencia_actual VARCHAR(100) DEFAULT NULL,
        fecha_registro DATETIME DEFAULT NULL,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if (!$conexion->query($tabla_sql)) {
        throw new Exception("Error al crear la tabla: " . $conexion->error);
    }
    
    // Preparar la consulta SQL para insertar en la tabla de prospectos
    $sql = "INSERT INTO inteletgroup_prospectos (
        nombre_ejecutivo, 
        rut_cliente, 
        razon_social, 
        rubro, 
        direccion_comercial, 
        telefono_celular, 
        email, 
        tipo_persona, 
        numero_pos, 
        tipo_cuenta, 
        numero_cuenta_bancaria, 
        dias_atencion, 
        horario_atencion, 
        contrata_boleta, 
        competencia_actual, 
        fecha_registro
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conexion->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Error al preparar la consulta: " . $conexion->error);
    }
    
    $stmt->bind_param("ssssssssssssssss", 
        $nombre_ejecutivo,
        $rut_cliente,
        $razon_social,
        $rubro,
        $direccion_comercial,
        $telefono_celular,
        $email,
        $tipo_persona,
        $numero_pos,
        $tipo_cuenta,
        $numero_cuenta_bancaria,
        $dias_atencion,
        $horario_atencion,
        $contrata_boleta,
        $competencia_actual,
        $fecha_registro
    );
    
    // Ejecutar la consulta
    if ($stmt->execute()) {
        $prospecto_id = $conexion->insert_id;
        
        // Manejo de archivos (versión simplificada)
        $documentos_procesados = [];
        
        // Si hay archivos adjuntos, procesarlos
        if (!empty($_FILES)) {
            // Esta parte manejaría la subida de archivos
            // Para la versión simplificada, solo registramos que se recibieron archivos
            $documentos_procesados[] = "Se recibieron archivos para procesar";
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Prospecto guardado exitosamente',
            'prospecto_id' => $prospecto_id,
            'documentos' => $documentos_procesados
        ]);
    } else {
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error al guardar el prospecto: ' . $e->getMessage()
    ]);
}

// Cerrar la conexión
$conexion->close();